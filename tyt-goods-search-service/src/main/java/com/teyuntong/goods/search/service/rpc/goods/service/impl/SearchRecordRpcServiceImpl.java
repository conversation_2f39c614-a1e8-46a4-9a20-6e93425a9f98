package com.teyuntong.goods.search.service.rpc.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.dispatch.entity.SpecialCarDispatchFailureDO;
import com.teyuntong.goods.search.service.biz.dispatch.service.SpecialCarDispatchFailureService;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.*;
import com.teyuntong.goods.search.service.biz.goods.service.*;
import com.teyuntong.goods.search.service.biz.record.constant.QuotedConstant;
import com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportQuotedPriceDO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportViewLogDO;
import com.teyuntong.goods.search.service.biz.record.mapper.AppCallLogMapper;
import com.teyuntong.goods.search.service.biz.record.mapper.TransportQuotedPriceMapper;
import com.teyuntong.goods.search.service.biz.record.mapper.TransportViewLogMapper;
import com.teyuntong.goods.search.service.biz.record.service.AppCallLogService;
import com.teyuntong.goods.search.service.biz.record.service.TransportQuotedPriceService;
import com.teyuntong.goods.search.service.biz.record.service.TransportViewLogService;
import com.teyuntong.goods.search.service.biz.record.vo.SearchRecordPageVO;
import com.teyuntong.goods.search.service.biz.record.vo.SearchRecordVO;
import com.teyuntong.goods.search.service.common.enums.ExcellentGoodsEnum;
import com.teyuntong.goods.search.service.common.enums.SearchRecordPageTypeEnum;
import com.teyuntong.goods.search.service.common.enums.YesNoEnum;
import com.teyuntong.goods.search.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.search.service.remote.order.OrderRemoteService;
import com.teyuntong.goods.search.service.remote.order.TradeRemoteService;
import com.teyuntong.goods.search.service.remote.user.SigningCarRemoteService;
import com.teyuntong.goods.search.service.rpc.goods.service.GoodsInfoRpcService;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchRecordRpcService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.redis.template.ObjectJsonRedisTemplate;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.trade.service.client.infofee.vo.TransportOrdersListVO;
import com.teyuntong.trade.service.client.orders.dto.CarGoodDealNumDTO;
import com.teyuntong.trade.service.client.orders.dto.TransportOrdersRpcDTO;
import com.teyuntong.trade.service.client.orders.enums.OrderPayStatusEnum;
import com.teyuntong.trade.service.client.orders.enums.OrderRobStatusEnum;
import com.teyuntong.trade.service.client.orders.vo.SpecialCarDispatchDetailVO;
import com.teyuntong.user.service.client.car.vo.TytSigningCarVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/10/11 10:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SearchRecordRpcServiceImpl implements SearchRecordRpcService {

    private final AppCallLogService appCallLogService;
    private final TransportViewLogService transportViewLogService;
    private final TransportQuotedPriceService transportQuotedPriceService;
    private final TransportService transportService;
    private final TransportMainService transportMainService;
    private final OrderRemoteService orderRemoteService;
    private final TradeRemoteService tradeRemoteService;
    private final ObjectJsonRedisTemplate redisTemplate;
    private final SpecialCarDispatchFailureService specialCarDispatchFailureService;
    private final ABTestRemoteService abTestRemoteService;
    private final SigningCarRemoteService signingCarRemoteService;
    private final RedisUtil redisUtil;
    private final TransportHistoryService transportHistoryService;
    private final TransportMainExtendService transportMainExtendService;
    private final TransportViewLogMapper transportViewLogMapper;
    private final TransportQuotedPriceMapper transportQuotedPriceMapper;
    private final AppCallLogMapper appCallLogMapper;

    // 是否有新报价
    public static final Predicate<TransportQuotedPriceDO> haveNewQuotedPredicate = t -> t.getFinalQuotedPriceIsDone() == 0 && Objects.equals(t.getCarQuotedPriceTimes(), t.getTransportQuotedPriceTimes());
    // 是否同意报价
    public static final Predicate<TransportQuotedPriceDO> haveAgreeQuotedPredicate = t -> t.getFinalQuotedPriceIsDone() == 1 && t.getFinalQuotedPriceType() == 1;

    private static final String ADD_PRICE_FILL_PRICE_VIEW_CACHE_KEY = "tyt-goods-search:add_price_fill_price_view_cache_key:";

    private static final String FREE_COMMISSION_VIEW_CACHE_KEY = "tyt-goods-search:free_commission_view_cache_key:";

    private static final String FREE_COMMISSION_NO_VIEW_CACHE_KEY = "tyt-goods-search:free_commission_no_view_cache_key:";

    /**
     * 查询找货记录
     */
    @Override
    public SearchRecordPageVO queryRecordList(SearchRecordQueryDTO queryDTO) {
        // 不登录直接返回空
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return new SearchRecordPageVO();
        }
        queryDTO.setUserId(loginUser.getUserId());

        SearchRecordPageVO pageVO;
        if (queryDTO.getViewLog()) {
            // 浏览记录，查询当天
            queryDTO.setStartDate(DateUtil.beginOfDay(new Date()));
            PageInfo<TransportViewLogDO> viewPage = transportViewLogService.getRecentViewPage(queryDTO);
            pageVO = getSearchRecordPageVO(viewPage, TransportViewLogDO::getTsId);

            Map<Long, TransportViewLogDO> map = viewPage.getList().stream().collect(Collectors.toMap(TransportViewLogDO::getTsId, t -> t));
            pageVO.getData().forEach(t -> t.setCallTime(map.get(t.getSrcMsgId()).getCtime()));
        } else if (queryDTO.getQuotedPriceOnce()) {
            // 出价记录，查询当天
            queryDTO.setStartDate(DateUtil.beginOfDay(new Date()));
            PageInfo<TransportQuotedPriceDO> quotedPage = transportQuotedPriceService.getRecentQuotedPage(queryDTO);
            pageVO = getSearchRecordPageVO(quotedPage, TransportQuotedPriceDO::getSrcMsgId);

            Map<Long, TransportQuotedPriceDO> map = quotedPage.getList().stream().collect(Collectors.toMap(TransportQuotedPriceDO::getSrcMsgId, t -> t));
            pageVO.getData().forEach(t -> {
                t.setCallTime(map.get(t.getSrcMsgId()).getCarQuotedPriceTime());
                // 删除未查看报价缓存
                redisTemplate.delete("transportQuotedPriceCarNoLook" + ":" + queryDTO.getUserId() + ":" + t.getSrcMsgId());
            });
        } else {
            // 拨打记录，查询当天
            queryDTO.setStartDate(DateUtil.beginOfDay(new Date()));
            PageInfo<AppCallLogDO> callPage = appCallLogService.getRecentCallPage(queryDTO);
            pageVO = getSearchRecordPageVO(callPage, AppCallLogDO::getSrcMsgId);

            Map<Long, AppCallLogDO> map = callPage.getList().stream().collect(Collectors.toMap(AppCallLogDO::getSrcMsgId, t -> t));
            pageVO.getData().forEach(t -> {
                AppCallLogDO appCallLogDO = map.get(t.getSrcMsgId());
                t.setCallTime(appCallLogDO.getCallTime());
                t.setCallResultCode(appCallLogDO.getCallResultCode());
                t.setCallResultName(appCallLogDO.getCallResultName());
                t.setReference(appCallLogDO.getReference());
            });
        }

        List<SearchRecordVO> searchRecordList = pageVO.getData();
        if (CollectionUtils.isEmpty(searchRecordList)) {
            return pageVO;
        }

        // 通用处理逻辑
        commonHandle(searchRecordList, queryDTO.getUserId());
        // 设置订单成交数和合作数
        setTradeAndCoopNums(searchRecordList, queryDTO.getUserId());
        // 设置是否显示详情页面的去支付按钮
        setShowPayBtn(searchRecordList, queryDTO.getUserId());

        // 对货源进行打标
        transportService.handleTransportTag(searchRecordList, queryDTO.getUserId());
        //由于APP并未给找货记录三个列表做昵称加密解密，所以目前这里调用hide方法时传参为不加密昵称
        transportService.hideSensitiveInfoIsShowTrueNickName(searchRecordList, queryDTO.getUserId(), false);
        //处理货源利益点标签
        transportService.dealBenefitLabel(searchRecordList);

        //将所有未查看免佣货源缓存全部转移到已查看免佣货源缓存中
        String today = DateUtil.formatDate(new Date());
        Set<String> noViewFreeCommissionSrcMsgIds = redisUtil.hashKeys(FREE_COMMISSION_NO_VIEW_CACHE_KEY + loginUser.getUserId() + ":" + today);
        if (!noViewFreeCommissionSrcMsgIds.isEmpty()) {
            noViewFreeCommissionSrcMsgIds.forEach(userId -> redisUtil.hashPut(FREE_COMMISSION_VIEW_CACHE_KEY + loginUser.getUserId() + ":" + today, userId, "1", Duration.ofHours(24)));
            redisUtil.delete(FREE_COMMISSION_NO_VIEW_CACHE_KEY + loginUser.getUserId() + ":" + today);
        }

        return pageVO;
    }


    /**
     * 根据货源srcMsgId查询数据，并构建页面VO
     */
    private <T> SearchRecordPageVO getSearchRecordPageVO(PageInfo<T> pageInfo, Function<T, Long> getSrcMsgIdFunc) {
        List<SearchRecordVO> searchRecordList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            List<Long> srcMsgIds = pageInfo.getList().stream().map(getSrcMsgIdFunc).toList();
            List<TransportMainDO> transportList = transportMainService.getBySrcMsgIds(srcMsgIds);
            Map<Long, TransportMainDO> transportMap = transportList.stream()
                    .collect(Collectors.toMap(TransportMainDO::getSrcMsgId,
                            Function.identity(), (v1, v2) -> v1));
            // 按照srcMsgIds的顺序排序
            for (Long srcMsgId : srcMsgIds) {
                TransportMainDO transportDO = transportMap.get(srcMsgId);
                if (transportDO != null) {
                    searchRecordList.add(TransportConverter.INSTANCE.transport2RecordVO(transportDO));
                }
            }
            // 设置扩展字段
            Map<Long, TransportMainExtendDO> extendDOMap = transportMainExtendService.getMapBySrcMsgIds(srcMsgIds);
            searchRecordList.forEach(vo -> TransportConverter.INSTANCE.transportMainExtend2VO(vo, extendDOMap.get(vo.getId())));
        }

        SearchRecordPageVO pageVO = new SearchRecordPageVO();
        pageVO.setCurrentPage(pageInfo.getPageNum());
        pageVO.setTotalRecord(pageInfo.getTotal());
        pageVO.setPageSize(pageInfo.getPageSize());
        pageVO.setMaxPage(pageInfo.getPages());
        pageVO.setData(searchRecordList);
        return pageVO;
    }

    /**
     * 通用处理逻辑
     */
    private void commonHandle(List<SearchRecordVO> searchRecordList, Long carUserId) {
        List<TransportQuotedPriceDO> todayQuoted = transportQuotedPriceService.getTodayQuoted(carUserId);
        Map<Long, List<TransportQuotedPriceDO>> todayQuotedGroup = todayQuoted.stream().collect(Collectors.groupingBy(TransportQuotedPriceDO::getSrcMsgId));

        List<Long> srcMsgIdList = searchRecordList.stream().map(SearchRecordVO::getSrcMsgId).toList();
        // 判断是否在专车签约车辆内，如果在里面才可以支付专车货源
        List<TytSigningCarVO> signingCarVOList = signingCarRemoteService.getByUserId(carUserId);
        List<SpecialCarDispatchFailureDO> dispatchList = specialCarDispatchFailureService.getBySrcMsgIds(srcMsgIdList);
        // 查询是否是指派货源如果是自动指派的货源，就跳过校验
        log.info("调用order校验是否是指派货源，srcMsgIdList:{},carUserId:{}", JSONUtil.toJsonStr(srcMsgIdList), carUserId);
        List<SpecialCarDispatchDetailVO> specialCarDispatchDetailVOList = orderRemoteService.specialCarDispatchGet(srcMsgIdList, carUserId);
        log.info("调用order校验是否是指派货源返回结果:{}", JSONUtil.toJsonStr(specialCarDispatchDetailVOList));

        boolean inABTest = abTestRemoteService.getInABTest("find_transport_price_change_bubble_abtest", carUserId);
        Map<Long, String> originalPriceMap = transportHistoryService.getOriginalPrice(srcMsgIdList);
        Map<Long, String> latestPriceMap = new HashMap<>();
        for (SearchRecordVO searchRecordVO : searchRecordList) {
            latestPriceMap.put(searchRecordVO.getSrcMsgId(), searchRecordVO.getPrice());
        }


        DateTime today = DateUtil.beginOfDay(new Date());
        for (SearchRecordVO searchRecordVO : searchRecordList) {
            searchRecordVO.setCallerId(carUserId);
            searchRecordVO.setTradeNums(searchRecordVO.getTradeNum());
            searchRecordVO.setPrice("".equals(searchRecordVO.getPrice()) ? "0" : searchRecordVO.getPrice());
            // 过期货源置为无效
            if (searchRecordVO.getGoodStatus() == 1 && searchRecordVO.getPubDate().before(today)) {
                searchRecordVO.setGoodStatus(0);
            }
            // 打标：已出价、有反馈
            List<TransportQuotedPriceDO> quotedList = todayQuotedGroup.get(searchRecordVO.getSrcMsgId());
            if (CollectionUtils.isNotEmpty(quotedList)) {
                searchRecordVO.setHaveNewTransportQuotedPrice(quotedList.stream().anyMatch(haveNewQuotedPredicate));
                searchRecordVO.setHaveAgreeTransportQuotedPrice(quotedList.stream().anyMatch(haveAgreeQuotedPredicate));
            }
            // 打标：有加价、补运费
            if (inABTest) {
                //在ab测试中，才返回有加价、补运费标签，并且增加已经查看过这些有加价、补运费货源的缓存
                haveFillInPriceOrAddPrice(originalPriceMap.get(searchRecordVO.getSrcMsgId()), latestPriceMap.get(searchRecordVO.getSrcMsgId()), searchRecordVO);
                if (searchRecordVO.getHaveAddPrice() || searchRecordVO.getHaveFillInPrice()) {
                    //增加该车方已经查看过这个补运费、有加价货源缓存
                    redisUtil.setIfAbsent(ADD_PRICE_FILL_PRICE_VIEW_CACHE_KEY + carUserId + ":" + searchRecordVO.getSrcMsgId(), "1", Duration.ofHours(24));
                }
            }

            // 是否可大厅抢单
            if (Objects.equals(searchRecordVO.getExcellentGoods(), ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode())) {
                // 判断是否在专车签约车辆内，如果在里面才可以支付专车货源
                if (CollUtil.isNotEmpty(signingCarVOList)) {
                    searchRecordVO.setDeclareInPublic(YesNoEnum.Y.getCode());

                } else if (CollUtil.isNotEmpty(dispatchList)) {
                    // 如果该货源标识了可大厅抢单，所有车辆都可以支付，否则，只有签约了的才能支付
                    Map<Long, Integer> dispatchMap = dispatchList.stream().collect(Collectors.toMap(SpecialCarDispatchFailureDO::getSrcMsgId, SpecialCarDispatchFailureDO::getDeclareInPublic));

                    searchRecordVO.setDeclareInPublic(dispatchMap.getOrDefault(searchRecordVO.getSrcMsgId(), 0));
                }
            }
            // 是否可以跳过权益校验
            if (CollUtil.isNotEmpty(specialCarDispatchDetailVOList)) {
                Set<Long> srcMsgIdSet = specialCarDispatchDetailVOList.stream().map(SpecialCarDispatchDetailVO::getTsId).collect(Collectors.toSet());
                if (srcMsgIdSet.contains(searchRecordVO.getSrcMsgId())) {
                    searchRecordVO.setSkipPermission(YesNoEnum.Y.getCode());
                }
            }
        }
    }

    private void haveFillInPriceOrAddPrice(String originPrice, String latestPrice, SearchRecordVO searchRecordVO) {

        BigDecimal originalPrice = StringUtils.isBlank(originPrice) ? BigDecimal.ZERO : new BigDecimal(originPrice);
        BigDecimal lastPrice = StringUtils.isBlank(latestPrice) ? BigDecimal.ZERO : new BigDecimal(latestPrice);

        if (originalPrice.compareTo(BigDecimal.ZERO) > 0) {
            //初始货源有价
            if (lastPrice.compareTo(BigDecimal.ZERO) > 0 && lastPrice.compareTo(originalPrice) > 0) {
                //最新货源有价并且比初始价格高
                searchRecordVO.setHaveAddPrice(true);
            }
        } else {
            //初始货源无价
            if (lastPrice.compareTo(BigDecimal.ZERO) > 0) {
                //最新货源有价
                searchRecordVO.setHaveFillInPrice(true);
            }
        }
    }

    /**
     * 设置订单成交数和合作数
     */
    private void setTradeAndCoopNums(List<SearchRecordVO> searchRecordList, Long carUserId) {
        // 查询车主和货主的合作数
        List<Long> goodsUserIds = searchRecordList.stream().map(SearchRecordVO::getUserId).toList();
        List<CarGoodDealNumDTO> dealNumList = tradeRemoteService.getDealNumOfCar(carUserId, goodsUserIds);
        if (CollectionUtils.isEmpty(dealNumList)) {
            return;
        }
        Map<Long, Integer> dealNumMap = dealNumList.stream().collect(Collectors.toMap(CarGoodDealNumDTO::getGoodsId, CarGoodDealNumDTO::getDealNum, (t1, t2) -> t1));

        for (SearchRecordVO searchRecordVO : searchRecordList) {
            // searchRecordVO.setTradeNums(userLocalService.getUserDealNum(searchRecordVO.getUserId()));
            searchRecordVO.setCoopNums(dealNumMap.getOrDefault(searchRecordVO.getUserId(), 0));
        }
    }

    /**
     * 设置是否显示详情页面的去支付按钮，0显示，1不显示
     */
    private void setShowPayBtn(List<SearchRecordVO> searchRecordList, Long carUserId) {
        // 根据运单号和车主id查询订单接口
        TransportOrdersRpcDTO ordersRpcDTO = new TransportOrdersRpcDTO();
        ordersRpcDTO.setPayUserId(carUserId);
        ordersRpcDTO.setTsOrderNoList(searchRecordList.stream().map(SearchRecordVO::getTsOrderNo).toList());
        ordersRpcDTO.setPayStatus(OrderPayStatusEnum.PAY_SUCCESS.getCode());
        List<TransportOrdersListVO> orderList = orderRemoteService.getByPayUserIdAndTsOrderNoList(ordersRpcDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        Map<String, String> orderRobStatusMap = orderList.stream().collect(Collectors.toMap(TransportOrdersListVO::getTsOrderNo, TransportOrdersListVO::getRobStatus, (t1, t2) -> t2));

        // 不显示条件： payStatus支付状态 =2支付成功，且 robStatus接单状态 != 2货主拒绝 3系统拒绝 12车方取消订单
        List<String> payFailStatuses = List.of(OrderRobStatusEnum.REFUSED.getCode(), OrderRobStatusEnum.SYSTEM_REFUSED.getCode(), OrderRobStatusEnum.CANCEL.getCode());

        for (SearchRecordVO searchRecordVO : searchRecordList) {
            String robStatus = orderRobStatusMap.get(searchRecordVO.getTsOrderNo());
            int isPaySuccess = (robStatus == null || payFailStatuses.contains(robStatus)) ? 0 : 1; // 0显示，1不显示
            searchRecordVO.setIsPaySuccess(isPaySuccess);
        }
    }

    // ===========================================================================================================

    /**
     * 车方 被反馈（报价被货方同意）、有回价 气泡提示
     *
     * @param userId 车主id
     */
    @Override
    public String quotedBubble(Long userId) {
        List<TransportQuotedPriceDO> todayQuoted = transportQuotedPriceService.getTodayQuoted(userId);
        if (CollectionUtils.isEmpty(todayQuoted)) {
            String result = getPriceChangeVule(userId);
            //运费气泡方法有可能返回空，如果返回空，则判断限时免佣气泡
            if (result.isEmpty()) {
                //获取限时免佣气泡内容
                return getFreeCommissionBubble(userId);
            }
        }
        throw new NumberFormatException()
        // 过滤掉无效货源
        List<Long> srcMsgIds = todayQuoted.stream().map(TransportQuotedPriceDO::getSrcMsgId).toList();
        Set<Long> validSrcMsgIds = Set.copyOf(transportService.filterValidTransport(srcMsgIds));

        // 最新的回价时间
        Date lastNewQuotedTime = getLastQuotedTime(todayQuoted, validSrcMsgIds, haveNewQuotedPredicate);
        // 最新的完成报价时间
        Date lastAgreeQuotedTime = getLastQuotedTime(todayQuoted, validSrcMsgIds, haveAgreeQuotedPredicate);

        if (lastNewQuotedTime != null && lastAgreeQuotedTime != null) {
            return lastAgreeQuotedTime.compareTo(lastNewQuotedTime) >= 0 ? "被反馈" : "有回价";
        }
        String result = lastNewQuotedTime != null ? "有回价" : lastAgreeQuotedTime != null ? "被反馈" : "";
        if (result.isEmpty()) {
            result = getPriceChangeVule(userId);
            if (result.isEmpty()) {
                //获取限时免佣气泡内容
                result = getFreeCommissionBubble(userId);
            }
        }
        return result;
    }

    @Override
    public String getFreeCommissionBubble(Long userId) {
        //获取浏览记录、拨打记录、出价记录三个列表的所有货源ID
        List<Long> AllRecordSrcMsgIdList = getAllRecordListSrcMsgIdByUserId(userId);
        if (CollectionUtils.isEmpty(AllRecordSrcMsgIdList)) {
            return "";
        }

        //获取这些货源中免佣的货源
        List<Long> freeCommissionSrcMsgIdList = transportMainService.getFreeCommissionTransportBySrcMsgIdList(AllRecordSrcMsgIdList);
        if (CollectionUtils.isEmpty(freeCommissionSrcMsgIdList)) {
            return "";
        }

        //获取这些免佣货源还处于发布中状态的货源
        List<Long> freeCommissionAndStatusSrcMsgIdList = transportService.filterValidTransport(freeCommissionSrcMsgIdList);
        if (CollectionUtils.isEmpty(freeCommissionSrcMsgIdList)) {
            return "";
        }

        //判断这些货源是否都在已查看免佣货源缓存中
        //如果有货源不在已查看免佣货源缓存中，将这些货源加入到未查看免佣货源缓存，并返回限时免佣气泡
        String today = DateUtil.formatDate(new Date());
        Set<Long> unViewFreeCommissionSrcMsgIdSet = freeCommissionAndStatusSrcMsgIdList.stream().filter(t -> redisUtil.hashGet(FREE_COMMISSION_VIEW_CACHE_KEY + userId + ":" + today, String.valueOf(t)) == null).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(unViewFreeCommissionSrcMsgIdSet)) {
            unViewFreeCommissionSrcMsgIdSet.forEach(t -> redisUtil.hashPut(FREE_COMMISSION_NO_VIEW_CACHE_KEY + userId + ":" + today, String.valueOf(t), "1", Duration.ofHours(24)));
            return "限时免佣";
        }
        return "";
    }

    private List<Long> getAllRecordListSrcMsgIdByUserId(Long userId) {
        DateTime dateTime = DateUtil.beginOfDay(new Date());
        List<Long> viewList = transportViewLogMapper.getRecentViewListSrcMsgId(userId, dateTime);
        List<Long> quotedList = transportQuotedPriceMapper.getRecentQuotedListSrcMsgId(userId, dateTime);
        List<Long> callList = appCallLogMapper.getRecentCallListSrcMsgId(userId, dateTime);
        //合并三个列表并且去重然后返回
        return Stream.of(viewList, quotedList, callList)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Integer getDefaultType(Long userId) {
        Date today = DateUtil.beginOfDay(new Date());
        Integer callCount = appCallLogService.getRecentCountByUserId(userId, today);
        if  (callCount > 0) {
            return SearchRecordPageTypeEnum.CALL_LOG.getType();
        }
        Integer quotedCount = transportQuotedPriceService.getRecentCountByUserId(userId, today);
        if (quotedCount > 0){
            return SearchRecordPageTypeEnum.QUOTED_PRICE.getType();
        }
        Integer viewCount = transportViewLogService.getRecentCountByUserId(userId, today);
        if (viewCount > 0){
            return SearchRecordPageTypeEnum.RECENT_VIEW.getType();
        }
        return SearchRecordPageTypeEnum.CALL_LOG.getType();
    }

    /**
     * 根据条件获取最新的报价时间
     */
    private Date getLastQuotedTime(List<TransportQuotedPriceDO> quotedList, Set<Long> validSrcMsgIds, Predicate<TransportQuotedPriceDO> filter) {
        return quotedList.stream()
                // 是否有效货源
                .filter(t -> validSrcMsgIds.contains(t.getSrcMsgId()))
                // 是否已完成出价
                .filter(filter)
                // 是否是未查看货源
                .filter(t -> Boolean.TRUE.equals(redisTemplate.hasKey(QuotedConstant.getCarNoLookCacheKey(t.getCarId(), t.getSrcMsgId())))).map(TransportQuotedPriceDO::getTransportQuotedPriceTime).sorted(Comparator.reverseOrder()).findAny().orElse(null);
    }

    private String getPriceChangeVule(Long userId) {
        //不在ab测试中，则不返回有加价、补运费标签
        if (!abTestRemoteService.getInABTest("find_transport_price_change_bubble_abtest", userId)) {
            return "";
        }
        //优先级有加价 > 补运费
        //如果该货首次发布transport表里无价，最近一次有价，则为补运费
        //如果该货首次发布transport表里有价，最近一次有价且价格更高，则为有加价
        List<Long> srcMsgIds = transportViewLogService.getRecentViewAllSrcMsgId(userId, DateUtil.beginOfDay(new Date()));

        boolean haveFillInPrice = false;

        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            Map<Long, String> originalPriceMap = transportHistoryService.getOriginalPrice(srcMsgIds);
            Map<Long, String> latestPriceMap = transportService.getLatestPrice(srcMsgIds);

            for (Long srcMsgId : srcMsgIds) {
                SearchRecordVO searchRecordVO = new SearchRecordVO();
                searchRecordVO.setSrcMsgId(srcMsgId);
                String value = redisUtil.getString(ADD_PRICE_FILL_PRICE_VIEW_CACHE_KEY + userId + ":" + searchRecordVO.getSrcMsgId());
                if (StringUtils.isNotBlank(value)) {
                    //如果缓存存在表示用户已在列表中查看过该货源，则不再返回气泡
                    continue;
                }
                haveFillInPriceOrAddPrice(originalPriceMap.get(srcMsgId), latestPriceMap.get(srcMsgId), searchRecordVO);
                if (searchRecordVO.getHaveAddPrice() != null && searchRecordVO.getHaveAddPrice()) {
                    return "有加价";
                }
                if (searchRecordVO.getHaveFillInPrice() != null && searchRecordVO.getHaveFillInPrice()) {
                    haveFillInPrice = true;
                }
            }
        }

        if (haveFillInPrice) {
            return "补运费";
        }
        return "";
    }

}

